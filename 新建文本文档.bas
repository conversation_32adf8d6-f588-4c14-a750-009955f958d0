Sub BatchGenerateReports()
    '打开word报告模板（可以自带测试区域照片）。各类图片单独放一个文件夹。数据的Excel单独放一个文件夹。
    '声明变量
    Dim xlApp As Object
    Dim xlWorkbook As Object
    Dim xlWorksheet As Object
    Dim templateDoc As Document
    Dim newDoc As Document
    Dim i As Long
    Dim lastRow As Long
    Dim reportName As String
    Dim rsrpValue As String
    Dim sinrValue As String

    '文件路径变量
    Dim dataFilePath As String
    Dim outputFolder As String
    Dim photoFolder As String
    Dim rsrpOutdoorFolder As String
    Dim sinrOutdoorFolder As String
    Dim rsrpIndoorFolder As String
    Dim sinrIndoorFolder As String

    '通过弹窗逐个选择每个文件路径
    If Not GetFilePaths(dataFilePath, outputFolder, photoFolder, rsrpOutdoorFolder, sinrOutdoorFolder, rsrpIndoorFolder, sinrIndoorFolder) Then
        MsgBox "操作已取消！", vbInformation
        Exit Sub
    End If
    
    '检查当前文档是否为模板
    If ActiveDocument.Name = "" Then
        MsgBox "请先打开模板文档！", vbExclamation
        Exit Sub
    End If
    
    '将当前文档作为模板
    Set templateDoc = ActiveDocument
    
    '创建Excel应用程序
    On Error Resume Next
    Set xlApp = CreateObject("Excel.Application")
    If xlApp Is Nothing Then
        MsgBox "无法创建Excel应用程序！", vbCritical
        Exit Sub
    End If
    On Error GoTo ErrorHandler
    
    xlApp.Visible = False
    
    '打开数据表
    Set xlWorkbook = xlApp.Workbooks.Open(dataFilePath)
    Set xlWorksheet = xlWorkbook.Worksheets(1)
    
    '获取数据行数
    lastRow = xlWorksheet.Cells(xlWorksheet.Rows.Count, 1).End(-4162).Row  'xlUp常量值
    
    '循环处理每一行数据
    For i = 2 To lastRow
        '读取数据
        reportName = Trim(CStr(xlWorksheet.Cells(i, 1).Value))
        rsrpValue = Trim(CStr(xlWorksheet.Cells(i, 2).Value))
        sinrValue = Trim(CStr(xlWorksheet.Cells(i, 3).Value))
        
        '跳过空行
        If reportName <> "" Then
            '复制整个模板文档内容
            Dim templateRange As Range
            Set templateRange = templateDoc.Range(0, templateDoc.Characters.Count)
            templateRange.Copy
            
            '创建新文档
            Set newDoc = Documents.Add
            
            '粘贴内容
            Selection.Paste
            
            '替换标题
            Call ReplaceTextInDoc(newDoc, "千喜室分测试报告", reportName & "室分测试报告")
            
            '替换RSRP和SINR数值
            Call ReplaceTextInDoc(newDoc, "RSRP平均-86.38dBm", "RSRP平均" & rsrpValue & "dBm")
            Call ReplaceTextInDoc(newDoc, "SINR平均8.73dBm", "SINR平均" & sinrValue & "dBm")
            
            '替换问题说明中的名称
            Call ReplaceTextInDoc(newDoc, "千喜室内进行覆盖测试", reportName & "室内进行覆盖测试")
            
            '插入图片（不同尺寸要求）
            '环境照片 - 保持原尺寸
            Call InsertPictureInDoc(newDoc, "环境照片", photoFolder & reportName & ".jpg", "environment")
            
            '室外测试图片 - 特定尺寸
            Call InsertPictureInDoc(newDoc, "RSRP图:", rsrpOutdoorFolder & reportName & ".jpg", "test")
            Call InsertPictureInDoc(newDoc, "SINR图:", sinrOutdoorFolder & reportName & ".jpg", "test")
            
            '处理室内测试图片 - 特定尺寸
            Call InsertPictureAfterText(newDoc, "室内测试情况如下：", "RSRP图:", rsrpIndoorFolder & reportName & ".jpg")
            Call InsertPictureAfterText(newDoc, "室内测试情况如下：", "SINR图:", sinrIndoorFolder & reportName & ".jpg")
            
            '保存文件
            newDoc.SaveAs2 outputFolder & reportName & ".docx"
            newDoc.Close False
            
            '更新进度
            Application.StatusBar = "正在处理: " & reportName & " (" & (i - 1) & "/" & (lastRow - 1) & ")"
        End If
    Next i
    
    '清理
    xlWorkbook.Close False
    xlApp.Quit
    Set xlWorksheet = Nothing
    Set xlWorkbook = Nothing
    Set xlApp = Nothing
    
    Application.StatusBar = False
    MsgBox "批量生成报告完成！共生成 " & (lastRow - 1) & " 份报告。", vbInformation
    Exit Sub
    
ErrorHandler:
    '错误处理
    If Not xlApp Is Nothing Then
        If Not xlWorkbook Is Nothing Then xlWorkbook.Close False
        xlApp.Quit
    End If
    MsgBox "发生错误: " & Err.Description, vbCritical
    Application.StatusBar = False
End Sub

'在指定文档中替换文本
Sub ReplaceTextInDoc(doc As Document, findText As String, replaceText As String)
    Dim findRange As Range
    Set findRange = doc.Range
    
    With findRange.Find
        .Text = findText
        .Replacement.Text = replaceText
        .Forward = True
        .Wrap = wdFindContinue
        .Format = False
        .MatchCase = False
        .MatchWholeWord = False
        .MatchWildcards = False
        .MatchSoundsLike = False
        .MatchAllWordForms = False
        .Execute Replace:=wdReplaceAll
    End With
End Sub

'在指定文档中插入图片（增强版）
Sub InsertPictureInDoc(doc As Document, searchText As String, imagePath As String, Optional pictureType As String = "test")
    Dim findRange As Range
    Set findRange = doc.Range
    
    '查找指定文本
    With findRange.Find
        .Text = searchText
        .Forward = True
        .Wrap = wdFindStop
        .Format = False
        .MatchCase = False
        .MatchWholeWord = False
        .Execute
    End With
    
    If findRange.Find.Found Then
        '移动到文本后面
        findRange.Collapse wdCollapseEnd
        findRange.InsertParagraphAfter
        findRange.Collapse wdCollapseEnd
        
        '确保没有项目符号
        With findRange.ParagraphFormat
            .LeftIndent = 0
            .FirstLineIndent = 0
            .SpaceAfter = 6
            .SpaceBefore = 6
        End With
        With findRange.ListFormat
            .RemoveNumbers wdNumberParagraph
        End With
        
        '设置段落居中
        findRange.ParagraphFormat.Alignment = wdAlignParagraphCenter
        
        '检查图片文件是否存在
        If Dir(imagePath) <> "" Then
            '插入图片
            Dim pic As InlineShape
            Set pic = findRange.InlineShapes.AddPicture(imagePath, False, True)
            
            '根据图片类型设置尺寸
            With pic
                If pictureType = "environment" Then
                    '环境照片保持默认比例，但设置合适大小
                ElseIf pictureType = "test" Then
                    '测试图片设置特定尺寸
                    .Width = CentimetersToPoints(13.89)  '13.89厘米宽
                    .Height = CentimetersToPoints(7.56)  '7.56厘米高
                Else
                    '默认尺寸
                    .Width = CentimetersToPoints(13.89)
                    .Height = CentimetersToPoints(7.56)
                End If
            End With
            
            '确保图片所在段落居中
            pic.Range.ParagraphFormat.Alignment = wdAlignParagraphCenter
            
        Else
            '如果图片不存在，插入提示文本（居中）
            findRange.Text = "[图片文件不存在: " & imagePath & "]"
            findRange.ParagraphFormat.Alignment = wdAlignParagraphCenter
        End If
    End If
End Sub

'在特定段落后插入图片（增强版）
Sub InsertPictureAfterText(doc As Document, sectionText As String, specificText As String, imagePath As String)
    Dim findRange As Range
    Dim searchRange As Range
    
    '首先找到段落
    Set findRange = doc.Range
    With findRange.Find
        .Text = sectionText
        .Forward = True
        .Wrap = wdFindStop
        .Execute
    End With
    
    If findRange.Find.Found Then
        '在该位置后查找特定文本
        Set searchRange = doc.Range(findRange.End, doc.Range.End)
        With searchRange.Find
            .Text = specificText
            .Forward = True
            .Wrap = wdFindStop
            .Execute
        End With
        
        If searchRange.Find.Found Then
            '在找到的文本后插入图片
            searchRange.Collapse wdCollapseEnd
            searchRange.InsertParagraphAfter
            searchRange.Collapse wdCollapseEnd
            
            '确保没有项目符号
            With searchRange.ParagraphFormat
                .LeftIndent = 0
                .FirstLineIndent = 0
                .SpaceAfter = 6
                .SpaceBefore = 6
                .Alignment = wdAlignParagraphCenter  '居中对齐
            End With
            With searchRange.ListFormat
                .RemoveNumbers wdNumberParagraph
            End With
            
            '检查图片文件是否存在
            If Dir(imagePath) <> "" Then
                Dim pic As InlineShape
                Set pic = searchRange.InlineShapes.AddPicture(imagePath, False, True)
                
                '设置测试图片尺寸
                With pic
                    .Width = CentimetersToPoints(13.89)   '13.89厘米宽
                    .Height = CentimetersToPoints(7.56)   '7.56厘米高
                End With
                
                '确保图片所在段落居中
                pic.Range.ParagraphFormat.Alignment = wdAlignParagraphCenter
                
            Else
                searchRange.Text = "[图片文件不存在: " & imagePath & "]"
                searchRange.ParagraphFormat.Alignment = wdAlignParagraphCenter
            End If
        End If
    End If
End Sub

'批量处理文档中所有图片的格式
Sub FormatAllPicturesInDoc(doc As Document)
    Dim pic As InlineShape
    Dim i As Integer
    
    '遍历文档中的所有内联图片
    For i = 1 To doc.InlineShapes.Count
        Set pic = doc.InlineShapes(i)
        If pic.Type = wdInlineShapePicture Then
            '设置图片居中
            pic.Range.ParagraphFormat.Alignment = wdAlignParagraphCenter
            
            '移除可能的项目符号
            With pic.Range.ListFormat
                .RemoveNumbers wdNumberParagraph
            End With
            
            '设置段落格式
            With pic.Range.ParagraphFormat
                .LeftIndent = 0
                .FirstLineIndent = 0
                .SpaceAfter = 6
                .SpaceBefore = 6
            End With
        End If
    Next i
End Sub

'设置向导和运行
Sub SetupAndRun()
    Dim response As Integer

    '检查是否有打开的文档
    If Documents.Count = 0 Then
        MsgBox "请先打开模板文档！", vbExclamation
        Exit Sub
    End If

    '显示使用说明
    response = MsgBox("批量测试报告生成器 - 弹窗选择版" & vbNewLine & vbNewLine & _
           "当前文档将作为模板使用。" & vbNewLine & _
           "新功能：" & vbNewLine & _
           "? 弹窗选择每个文件和文件夹，无需手动复制路径" & vbNewLine & _
           "? 逐个选择所有必要的路径" & vbNewLine & vbNewLine & _
           "图片设置说明：" & vbNewLine & _
           "? 所有图片将居中显示" & vbNewLine & _
           "? 环境照片：保持原比例" & vbNewLine & _
           "? 测试图片：13.89cm×7.56cm" & vbNewLine & _
           "? 图片前不会添加项目符号" & vbNewLine & vbNewLine & _
           "操作流程（将依次弹出选择窗口）：" & vbNewLine & _
           "1. 选择Excel数据文件" & vbNewLine & _
           "2. 选择报告输出文件夹" & vbNewLine & _
           "3. 选择环境照片文件夹" & vbNewLine & _
           "4. 选择室外RSRP图文件夹" & vbNewLine & _
           "5. 选择室外SINR图文件夹" & vbNewLine & _
           "6. 选择室内RSRP图文件夹" & vbNewLine & _
           "7. 选择室内SINR图文件夹" & vbNewLine & vbNewLine & _
           "是否开始？", vbYesNo + vbQuestion, "弹窗选择批量生成")

    If response = vbYes Then
        Call BatchGenerateReports
    End If
End Sub

'测试图片插入和格式设置
Sub TestPictureFormat()
    '用于测试图片格式设置功能
    If Documents.Count = 0 Then
        MsgBox "请先打开一个文档！"
        Exit Sub
    End If
    
    Call FormatAllPicturesInDoc(ActiveDocument)
    MsgBox "图片格式设置完成！所有图片已居中并移除项目符号。"
End Sub

'手动格式化当前文档中的所有图片
Sub FormatCurrentDocPictures()
    If Documents.Count = 0 Then
        MsgBox "请先打开一个文档！"
        Exit Sub
    End If

    Call FormatAllPicturesInDoc(ActiveDocument)
    MsgBox "当前文档中的图片格式已更新！"
End Sub

'通过弹窗获取所有文件路径
Function GetFilePaths(ByRef dataFilePath As String, ByRef outputFolder As String, _
                     ByRef photoFolder As String, ByRef rsrpOutdoorFolder As String, _
                     ByRef sinrOutdoorFolder As String, ByRef rsrpIndoorFolder As String, _
                     ByRef sinrIndoorFolder As String) As Boolean

    Dim fd As FileDialog
    Dim result As Boolean
    result = True

    '1. 选择Excel数据文件
    Set fd = Application.FileDialog(msoFileDialogFilePicker)
    With fd
        .Title = "请选择Excel数据文件"
        .Filters.Clear
        .Filters.Add "Excel文件", "*.xlsx;*.xls"
        .AllowMultiSelect = False
        If .Show = -1 Then
            dataFilePath = .SelectedItems(1)
        Else
            result = False
            GoTo CleanUp
        End If
    End With

    '2. 选择输出文件夹
    Set fd = Application.FileDialog(msoFileDialogFolderPicker)
    With fd
        .Title = "请选择报告输出文件夹"
        If .Show = -1 Then
            outputFolder = .SelectedItems(1) & "\"
        Else
            result = False
            GoTo CleanUp
        End If
    End With

    '3. 选择环境照片文件夹
    Set fd = Application.FileDialog(msoFileDialogFolderPicker)
    With fd
        .Title = "请选择环境照片文件夹"
        If .Show = -1 Then
            photoFolder = .SelectedItems(1) & "\"
        Else
            result = False
            GoTo CleanUp
        End If
    End With

    '4. 选择室外RSRP图文件夹
    Set fd = Application.FileDialog(msoFileDialogFolderPicker)
    With fd
        .Title = "请选择室外RSRP图文件夹"
        If .Show = -1 Then
            rsrpOutdoorFolder = .SelectedItems(1) & "\"
        Else
            result = False
            GoTo CleanUp
        End If
    End With

    '5. 选择室外SINR图文件夹
    Set fd = Application.FileDialog(msoFileDialogFolderPicker)
    With fd
        .Title = "请选择室外SINR图文件夹"
        If .Show = -1 Then
            sinrOutdoorFolder = .SelectedItems(1) & "\"
        Else
            result = False
            GoTo CleanUp
        End If
    End With

    '6. 选择室内RSRP图文件夹
    Set fd = Application.FileDialog(msoFileDialogFolderPicker)
    With fd
        .Title = "请选择室内RSRP图文件夹"
        If .Show = -1 Then
            rsrpIndoorFolder = .SelectedItems(1) & "\"
        Else
            result = False
            GoTo CleanUp
        End If
    End With

    '7. 选择室内SINR图文件夹
    Set fd = Application.FileDialog(msoFileDialogFolderPicker)
    With fd
        .Title = "请选择室内SINR图文件夹"
        If .Show = -1 Then
            sinrIndoorFolder = .SelectedItems(1) & "\"
        Else
            result = False
            GoTo CleanUp
        End If
    End With

CleanUp:
    Set fd = Nothing
    GetFilePaths = result
End Function

'简化版路径选择（如果您希望一次性选择主文件夹）
Function GetSimplifiedPaths(ByRef dataFilePath As String, ByRef mainFolder As String, _
                           ByRef outputFolder As String, ByRef photoFolder As String, _
                           ByRef rsrpOutdoorFolder As String, ByRef sinrOutdoorFolder As String, _
                           ByRef rsrpIndoorFolder As String, ByRef sinrIndoorFolder As String) As Boolean

    Dim fd As FileDialog
    Dim result As Boolean
    result = True

    '1. 选择Excel数据文件
    Set fd = Application.FileDialog(msoFileDialogFilePicker)
    With fd
        .Title = "请选择Excel数据文件"
        .Filters.Clear
        .Filters.Add "Excel文件", "*.xlsx;*.xls"
        .AllowMultiSelect = False
        If .Show = -1 Then
            dataFilePath = .SelectedItems(1)
        Else
            result = False
            GoTo CleanUp
        End If
    End With

    '2. 选择主工作文件夹
    Set fd = Application.FileDialog(msoFileDialogFolderPicker)
    With fd
        .Title = "请选择主工作文件夹（包含报告和图片子文件夹）"
        If .Show = -1 Then
            mainFolder = .SelectedItems(1)
            '自动构建子文件夹路径
            outputFolder = mainFolder & "\报告\"
            photoFolder = mainFolder & "\图片\环境图片\"
            rsrpOutdoorFolder = mainFolder & "\图片\室外RSRP\"
            sinrOutdoorFolder = mainFolder & "\图片\室外SINR\"
            rsrpIndoorFolder = mainFolder & "\图片\RSRP\"
            sinrIndoorFolder = mainFolder & "\图片\SINR\"
        Else
            result = False
            GoTo CleanUp
        End If
    End With

CleanUp:
    Set fd = Nothing
    GetSimplifiedPaths = result
End Function

'确保所有必要的文件夹存在
Sub EnsureFoldersExist(outputFolder As String, photoFolder As String, _
                      rsrpOutdoorFolder As String, sinrOutdoorFolder As String, _
                      rsrpIndoorFolder As String, sinrIndoorFolder As String)

    '创建文件夹的辅助函数
    If Dir(outputFolder, vbDirectory) = "" Then
        MkDir outputFolder
    End If

    If Dir(photoFolder, vbDirectory) = "" Then
        CreateFolderPath photoFolder
    End If

    If Dir(rsrpOutdoorFolder, vbDirectory) = "" Then
        CreateFolderPath rsrpOutdoorFolder
    End If

    If Dir(sinrOutdoorFolder, vbDirectory) = "" Then
        CreateFolderPath sinrOutdoorFolder
    End If

    If Dir(rsrpIndoorFolder, vbDirectory) = "" Then
        CreateFolderPath rsrpIndoorFolder
    End If

    If Dir(sinrIndoorFolder, vbDirectory) = "" Then
        CreateFolderPath sinrIndoorFolder
    End If
End Sub

'递归创建文件夹路径
Sub CreateFolderPath(folderPath As String)
    Dim pathParts() As String
    Dim currentPath As String
    Dim i As Integer

    '移除末尾的反斜杠
    If Right(folderPath, 1) = "\" Then
        folderPath = Left(folderPath, Len(folderPath) - 1)
    End If

    pathParts = Split(folderPath, "\")
    currentPath = pathParts(0) & "\"

    For i = 1 To UBound(pathParts)
        currentPath = currentPath & pathParts(i) & "\"
        If Dir(currentPath, vbDirectory) = "" Then
            MkDir currentPath
        End If
    Next i
End Sub

'带有路径输入框的版本（备用方案）
Function GetPathsWithInputBox(ByRef dataFilePath As String, ByRef outputFolder As String, _
                             ByRef photoFolder As String, ByRef rsrpOutdoorFolder As String, _
                             ByRef sinrOutdoorFolder As String, ByRef rsrpIndoorFolder As String, _
                             ByRef sinrIndoorFolder As String) As Boolean

    Dim userInput As String
    Dim result As Boolean
    result = True

    '1. Excel数据文件路径
    userInput = InputBox("请输入Excel数据文件的完整路径：", "Excel数据文件路径", "")
    If userInput = "" Then
        result = False
        GoTo ExitFunction
    End If
    dataFilePath = userInput

    '2. 主工作文件夹路径
    userInput = InputBox("请输入主工作文件夹路径（将自动创建子文件夹）：", "主工作文件夹路径", "")
    If userInput = "" Then
        result = False
        GoTo ExitFunction
    End If

    '确保路径以反斜杠结尾
    If Right(userInput, 1) <> "\" Then
        userInput = userInput & "\"
    End If

    '自动构建子文件夹路径
    outputFolder = userInput & "报告\"
    photoFolder = userInput & "图片\环境图片\"
    rsrpOutdoorFolder = userInput & "图片\室外RSRP\"
    sinrOutdoorFolder = userInput & "图片\室外SINR\"
    rsrpIndoorFolder = userInput & "图片\RSRP\"
    sinrIndoorFolder = userInput & "图片\SINR\"

ExitFunction:
    GetPathsWithInputBox = result
End Function

'使用输入框版本的批量生成（备用方案）
Sub BatchGenerateReportsWithInputBox()
    '打开word报告模板（可以自带测试区域照片）。各类图片单独放一个文件夹。数据的Excel单独放一个文件夹。
    '声明变量
    Dim xlApp As Object
    Dim xlWorkbook As Object
    Dim xlWorksheet As Object
    Dim templateDoc As Document
    Dim newDoc As Document
    Dim i As Long
    Dim lastRow As Long
    Dim reportName As String
    Dim rsrpValue As String
    Dim sinrValue As String

    '文件路径变量
    Dim dataFilePath As String
    Dim outputFolder As String
    Dim photoFolder As String
    Dim rsrpOutdoorFolder As String
    Dim sinrOutdoorFolder As String
    Dim rsrpIndoorFolder As String
    Dim sinrIndoorFolder As String

    '通过输入框获取文件路径
    If Not GetPathsWithInputBox(dataFilePath, outputFolder, photoFolder, rsrpOutdoorFolder, sinrOutdoorFolder, rsrpIndoorFolder, sinrIndoorFolder) Then
        MsgBox "操作已取消！", vbInformation
        Exit Sub
    End If

    '检查必要的文件夹是否存在，如果不存在则创建
    Call EnsureFoldersExist(outputFolder, photoFolder, rsrpOutdoorFolder, sinrOutdoorFolder, rsrpIndoorFolder, sinrIndoorFolder)

    '检查当前文档是否为模板
    If ActiveDocument.Name = "" Then
        MsgBox "请先打开模板文档！", vbExclamation
        Exit Sub
    End If

    '将当前文档作为模板
    Set templateDoc = ActiveDocument

    '创建Excel应用程序
    On Error Resume Next
    Set xlApp = CreateObject("Excel.Application")
    If xlApp Is Nothing Then
        MsgBox "无法创建Excel应用程序！", vbCritical
        Exit Sub
    End If
    On Error GoTo ErrorHandler

    xlApp.Visible = False

    '打开数据表
    Set xlWorkbook = xlApp.Workbooks.Open(dataFilePath)
    Set xlWorksheet = xlWorkbook.Worksheets(1)

    '获取数据行数
    lastRow = xlWorksheet.Cells(xlWorksheet.Rows.Count, 1).End(-4162).Row  'xlUp常量值

    '循环处理每一行数据
    For i = 2 To lastRow
        '读取数据
        reportName = Trim(CStr(xlWorksheet.Cells(i, 1).Value))
        rsrpValue = Trim(CStr(xlWorksheet.Cells(i, 2).Value))
        sinrValue = Trim(CStr(xlWorksheet.Cells(i, 3).Value))

        '跳过空行
        If reportName <> "" Then
            '复制整个模板文档内容
            Dim templateRange As Range
            Set templateRange = templateDoc.Range(0, templateDoc.Characters.Count)
            templateRange.Copy

            '创建新文档
            Set newDoc = Documents.Add

            '粘贴内容
            Selection.Paste

            '替换标题
            Call ReplaceTextInDoc(newDoc, "千喜室分测试报告", reportName & "室分测试报告")

            '替换RSRP和SINR数值
            Call ReplaceTextInDoc(newDoc, "RSRP平均-86.38dBm", "RSRP平均" & rsrpValue & "dBm")
            Call ReplaceTextInDoc(newDoc, "SINR平均8.73dBm", "SINR平均" & sinrValue & "dBm")

            '替换问题说明中的名称
            Call ReplaceTextInDoc(newDoc, "千喜室内进行覆盖测试", reportName & "室内进行覆盖测试")

            '插入图片（不同尺寸要求）
            '环境照片 - 保持原尺寸
            Call InsertPictureInDoc(newDoc, "环境照片", photoFolder & reportName & ".jpg", "environment")

            '室外测试图片 - 特定尺寸
            Call InsertPictureInDoc(newDoc, "RSRP图:", rsrpOutdoorFolder & reportName & ".jpg", "test")
            Call InsertPictureInDoc(newDoc, "SINR图:", sinrOutdoorFolder & reportName & ".jpg", "test")

            '处理室内测试图片 - 特定尺寸
            Call InsertPictureAfterText(newDoc, "室内测试情况如下：", "RSRP图:", rsrpIndoorFolder & reportName & ".jpg")
            Call InsertPictureAfterText(newDoc, "室内测试情况如下：", "SINR图:", sinrIndoorFolder & reportName & ".jpg")

            '保存文件
            newDoc.SaveAs2 outputFolder & reportName & ".docx"
            newDoc.Close False

            '更新进度
            Application.StatusBar = "正在处理: " & reportName & " (" & (i - 1) & "/" & (lastRow - 1) & ")"
        End If
    Next i

    '清理
    xlWorkbook.Close False
    xlApp.Quit
    Set xlWorksheet = Nothing
    Set xlWorkbook = Nothing
    Set xlApp = Nothing

    Application.StatusBar = False
    MsgBox "批量生成报告完成！共生成 " & (lastRow - 1) & " 份报告。", vbInformation
    Exit Sub

ErrorHandler:
    '错误处理
    If Not xlApp Is Nothing Then
        If Not xlWorkbook Is Nothing Then xlWorkbook.Close False
        xlApp.Quit
    End If
    MsgBox "发生错误: " & Err.Description, vbCritical
    Application.StatusBar = False
End Sub

